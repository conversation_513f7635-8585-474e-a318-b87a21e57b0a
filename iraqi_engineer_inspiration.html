<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>رحلة مهندس عراقي - إلهام وحكمة من ألمانيا</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Noto+Sans+Arabic:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans Arabic', '<PERSON><PERSON>', Aria<PERSON>, sans-serif;
            line-height: 1.8;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header Styles */
        header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: #4a5568;
            font-family: 'Amiri', serif;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            text-decoration: none;
            color: #4a5568;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover {
            color: #667eea;
        }

        /* Hero Section */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.3);
            z-index: 1;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            max-width: 800px;
        }

        .engineer-photo {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            margin: 0 auto 2rem;
            background: linear-gradient(45deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 4rem;
            color: white;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            font-family: 'Amiri', serif;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .hero p {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .cta-button {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }

        /* Section Styles */
        .section {
            padding: 80px 0;
            background: white;
        }

        .section:nth-child(even) {
            background: #f8f9fa;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
            color: #2d3748;
            font-family: 'Amiri', serif;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .content-card {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-right: 4px solid #667eea;
        }

        .content-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }

        .card-icon {
            font-size: 2.5rem;
            color: #667eea;
            margin-bottom: 1rem;
        }

        .card-title {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #2d3748;
            font-weight: 600;
        }

        .card-content {
            color: #4a5568;
            line-height: 1.8;
        }

        /* Quote Section */
        .quote-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 80px 0;
        }

        .quote {
            font-size: 2rem;
            font-style: italic;
            margin-bottom: 2rem;
            font-family: 'Amiri', serif;
            max-width: 800px;
            margin: 0 auto 2rem;
        }

        .quote-author {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        /* Timeline Styles */
        .timeline {
            position: relative;
            max-width: 800px;
            margin: 0 auto;
        }

        .timeline::after {
            content: '';
            position: absolute;
            width: 4px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            top: 0;
            bottom: 0;
            right: 50%;
            margin-right: -2px;
        }

        .timeline-item {
            padding: 10px 40px;
            position: relative;
            background: inherit;
            width: 50%;
        }

        .timeline-item::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            right: -10px;
            background: #667eea;
            border: 4px solid white;
            top: 15px;
            border-radius: 50%;
            z-index: 1;
        }

        .timeline-item:nth-child(even) {
            left: 50%;
        }

        .timeline-item:nth-child(odd) {
            left: 0;
        }

        .timeline-content {
            padding: 20px 30px;
            background: white;
            position: relative;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        /* Footer */
        footer {
            background: #2d3748;
            color: white;
            text-align: center;
            padding: 40px 0;
        }

        .social-links {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .social-links a {
            display: inline-block;
            width: 50px;
            height: 50px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            transition: transform 0.3s ease;
        }

        .social-links a:hover {
            transform: scale(1.1);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .hero h1 {
                font-size: 2rem;
            }

            .hero p {
                font-size: 1.1rem;
            }

            .section-title {
                font-size: 2rem;
            }

            .timeline::after {
                right: 31px;
            }

            .timeline-item {
                width: 100%;
                padding-right: 70px;
                padding-left: 25px;
            }

            .timeline-item::after {
                right: 22px;
            }

            .timeline-item:nth-child(even) {
                left: 0%;
            }

            .quote {
                font-size: 1.5rem;
            }
        }

        /* Smooth Scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Animation Classes */
        .fade-in {
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.6s ease, transform 0.6s ease;
        }

        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <nav class="container">
            <div class="logo">مهندس عراقي في ألمانيا</div>
            <ul class="nav-links">
                <li><a href="#home">الرئيسية</a></li>
                <li><a href="#journey">الرحلة</a></li>
                <li><a href="#advice">النصائح</a></li>
                <li><a href="#culture">الثقافة</a></li>
                <li><a href="#contact">التواصل</a></li>
            </ul>
        </nav>
    </header>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-content">
            <div class="engineer-photo">
                <i class="fas fa-user-tie"></i>
            </div>
            <h1>أحمد محمد الخالدي</h1>
            <p>مهندس ميكانيكي عراقي - عشر سنوات من الخبرة في ألمانيا</p>
            <p>"من بغداد إلى برلين: رحلة إلهام وتحدي"</p>
            <a href="#journey" class="cta-button">اكتشف رحلتي</a>
        </div>
    </section>

    <!-- Journey Section -->
    <section id="journey" class="section">
        <div class="container">
            <h2 class="section-title">رحلتي من العراق إلى ألمانيا</h2>
            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-content">
                        <h3>2014 - البداية في بغداد</h3>
                        <p>تخرجت من كلية الهندسة الميكانيكية في جامعة بغداد بتقدير امتياز. كان حلمي دائماً أن أطور مهاراتي في بيئة تقنية متقدمة، وكانت ألمانيا وجهتي المثالية بسبب تقدمها الصناعي والتقني.</p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-content">
                        <h3>2015 - الوصول إلى ألمانيا</h3>
                        <p>وصلت إلى برلين بحلم كبير وحقيبة صغيرة. التحدي الأول كان تعلم اللغة الألمانية والتكيف مع الثقافة الجديدة. بدأت بدورة مكثفة في اللغة الألمانية وعملت في وظائف بسيطة لتغطية نفقات المعيشة.</p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-content">
                        <h3>2016-2018 - التعليم والتطوير</h3>
                        <p>التحقت ببرنامج الماجستير في الهندسة الميكانيكية في جامعة برلين التقنية. كانت فترة صعبة لكنها مثمرة، حيث تعلمت أحدث التقنيات في مجال الهندسة وطورت مهاراتي في البحث والتطوير.</p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-content">
                        <h3>2019-2024 - النجاح المهني</h3>
                        <p>حصلت على وظيفة في شركة BMW كمهندس تطوير. خلال هذه السنوات، عملت على مشاريع مبتكرة في مجال السيارات الكهربائية والتقنيات المستدامة. أصبحت قائد فريق وحصلت على عدة براءات اختراع.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Quote Section -->
    <section class="quote-section">
        <div class="container">
            <div class="quote">
                "النجاح ليس وجهة، بل رحلة مستمرة من التعلم والتطوير. كل تحدٍ واجهته في ألمانيا جعلني أقوى وأكثر حكمة."
            </div>
            <div class="quote-author">- أحمد محمد الخالدي</div>
        </div>
    </section>

    <!-- Life Lessons Section -->
    <section id="advice" class="section">
        <div class="container">
            <h2 class="section-title">دروس الحياة والحكمة المكتسبة</h2>
            <div class="content-grid">
                <div class="content-card fade-in">
                    <div class="card-icon">
                        <i class="fas fa-lightbulb"></i>
                    </div>
                    <h3 class="card-title">التعلم المستمر</h3>
                    <div class="card-content">
                        <p>في ألمانيا تعلمت أن التعلم لا يتوقف عند التخرج. الاستثمار في تطوير المهارات والمعرفة هو مفتاح النجاح في أي مجال. احرص على تعلم شيء جديد كل يوم، سواء كان تقنياً أو ثقافياً.</p>
                    </div>
                </div>
                <div class="content-card fade-in">
                    <div class="card-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <h3 class="card-title">الصبر والمثابرة</h3>
                    <div class="card-content">
                        <p>الطريق إلى النجاح ليس سهلاً، خاصة في بلد جديد. واجهت صعوبات كثيرة في البداية، لكن الصبر والمثابرة كانا سلاحي الأقوى. لا تستسلم أمام التحديات، فهي جزء من رحلة النمو.</p>
                    </div>
                </div>
                <div class="content-card fade-in">
                    <div class="card-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="card-title">بناء العلاقات</h3>
                    <div class="card-content">
                        <p>العلاقات الإنسانية هي أساس النجاح في أي مكان. تعلمت أهمية بناء شبكة علاقات قوية مع الزملاء والأصدقاء. كن منفتحاً ومتعاوناً، وستجد أن الناس مستعدون لمساعدتك.</p>
                    </div>
                </div>
                <div class="content-card fade-in">
                    <div class="card-icon">
                        <i class="fas fa-balance-scale"></i>
                    </div>
                    <h3 class="card-title">التوازن في الحياة</h3>
                    <div class="card-content">
                        <p>تعلمت في ألمانيا أهمية التوازن بين العمل والحياة الشخصية. النجاح المهني مهم، لكن الصحة النفسية والجسدية والعلاقات الأسرية لا تقل أهمية. خصص وقتاً لنفسك ولأحبائك.</p>
                    </div>
                </div>
                <div class="content-card fade-in">
                    <div class="card-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <h3 class="card-title">الانفتاح الثقافي</h3>
                    <div class="card-content">
                        <p>العيش في ثقافة مختلفة علمني قيمة الانفتاح والتسامح. كل ثقافة لها ما تقدمه من حكمة وخبرة. كن متقبلاً للاختلاف ومستعداً لتعلم وجهات نظر جديدة.</p>
                    </div>
                </div>
                <div class="content-card fade-in">
                    <div class="card-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <h3 class="card-title">الثقة بالنفس</h3>
                    <div class="card-content">
                        <p>أهم درس تعلمته هو الثقة بالنفس والقدرات. نحن كعرب وعراقيين لدينا إمكانيات هائلة وعقول مبدعة. لا تدع أحداً يقلل من شأنك أو يحد من طموحاتك. آمن بنفسك وبقدرتك على تحقيق أحلامك.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Career Advice Section -->
    <section class="section">
        <div class="container">
            <h2 class="section-title">نصائح مهنية للمهندسين الشباب</h2>
            <div class="content-grid">
                <div class="content-card fade-in">
                    <div class="card-icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <h3 class="card-title">التخصص والتميز</h3>
                    <div class="card-content">
                        <p>اختر مجالاً تحبه وتميز فيه. في ألمانيا، يقدرون الخبرة العميقة أكثر من المعرفة السطحية. ركز على تطوير مهاراتك في مجال محدد وكن الأفضل فيه.</p>
                        <ul style="margin-top: 1rem; padding-right: 1rem;">
                            <li>احرص على الحصول على شهادات مهنية معترف بها</li>
                            <li>شارك في المؤتمرات والورش التدريبية</li>
                            <li>اقرأ أحدث الأبحاث في مجالك</li>
                        </ul>
                    </div>
                </div>
                <div class="content-card fade-in">
                    <div class="card-icon">
                        <i class="fas fa-language"></i>
                    </div>
                    <h3 class="card-title">إتقان اللغة</h3>
                    <div class="card-content">
                        <p>اللغة الألمانية مفتاح النجاح في ألمانيا. لا تكتفِ بالمستوى الأساسي، بل اسعَ لإتقان اللغة التقنية في مجالك.</p>
                        <ul style="margin-top: 1rem; padding-right: 1rem;">
                            <li>احصل على شهادة C1 على الأقل</li>
                            <li>تعلم المصطلحات التقنية في مجالك</li>
                            <li>مارس اللغة يومياً مع الزملاء</li>
                        </ul>
                    </div>
                </div>
                <div class="content-card fade-in">
                    <div class="card-icon">
                        <i class="fas fa-network-wired"></i>
                    </div>
                    <h3 class="card-title">بناء الشبكة المهنية</h3>
                    <div class="card-content">
                        <p>الشبكة المهنية في ألمانيا مهمة جداً. انضم للجمعيات المهنية وشارك في الفعاليات.</p>
                        <ul style="margin-top: 1rem; padding-right: 1rem;">
                            <li>انضم لـ VDI (جمعية المهندسين الألمان)</li>
                            <li>استخدم LinkedIn بفعالية</li>
                            <li>احضر معارض التوظيف والفعاليات المهنية</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Cultural Insights Section -->
    <section id="culture" class="section">
        <div class="container">
            <h2 class="section-title">نظرة على الثقافة الألمانية في العمل</h2>
            <div class="content-grid">
                <div class="content-card fade-in">
                    <div class="card-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3 class="card-title">الدقة والانضباط</h3>
                    <div class="card-content">
                        <p>الألمان يقدرون الدقة في المواعيد والالتزام بالخطط. كن دائماً في الوقت المحدد، وإذا كان لديك موعد في الساعة 9:00، كن هناك في 8:55.</p>
                    </div>
                </div>
                <div class="content-card fade-in">
                    <div class="card-icon">
                        <i class="fas fa-handshake"></i>
                    </div>
                    <h3 class="card-title">التواصل المباشر</h3>
                    <div class="card-content">
                        <p>الألمان يفضلون التواصل المباشر والصريح. لا تخف من التعبير عن رأيك بوضوح، لكن بطريقة محترمة ومهنية.</p>
                    </div>
                </div>
                <div class="content-card fade-in">
                    <div class="card-icon">
                        <i class="fas fa-balance-scale"></i>
                    </div>
                    <h3 class="card-title">التوازن بين العمل والحياة</h3>
                    <div class="card-content">
                        <p>في ألمانيا، يحترمون الحدود بين العمل والحياة الشخصية. لا تتوقع رداً على الإيميلات بعد ساعات العمل أو في عطلة نهاية الأسبوع.</p>
                    </div>
                </div>
                <div class="content-card fade-in">
                    <div class="card-icon">
                        <i class="fas fa-recycle"></i>
                    </div>
                    <h3 class="card-title">الاستدامة والبيئة</h3>
                    <div class="card-content">
                        <p>الألمان يهتمون كثيراً بالبيئة والاستدامة. تعلم عن أنظمة إعادة التدوير واحترم القوانين البيئية.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Practical Tips Section -->
    <section class="section">
        <div class="container">
            <h2 class="section-title">نصائح عملية للدراسة والعمل في ألمانيا</h2>
            <div class="content-grid">
                <div class="content-card fade-in">
                    <div class="card-icon">
                        <i class="fas fa-university"></i>
                    </div>
                    <h3 class="card-title">اختيار الجامعة</h3>
                    <div class="card-content">
                        <p>ابحث جيداً عن الجامعات والبرامج المناسبة لك:</p>
                        <ul style="margin-top: 1rem; padding-right: 1rem;">
                            <li>جامعة برلين التقنية (TU Berlin)</li>
                            <li>جامعة ميونخ التقنية (TUM)</li>
                            <li>جامعة آخن (RWTH Aachen)</li>
                            <li>جامعة شتوتغارت</li>
                        </ul>
                    </div>
                </div>
                <div class="content-card fade-in">
                    <div class="card-icon">
                        <i class="fas fa-euro-sign"></i>
                    </div>
                    <h3 class="card-title">التمويل والمنح</h3>
                    <div class="card-content">
                        <p>هناك عدة خيارات للتمويل:</p>
                        <ul style="margin-top: 1rem; padding-right: 1rem;">
                            <li>منح DAAD</li>
                            <li>منح الحكومة الألمانية</li>
                            <li>العمل الجزئي (450 يورو شهرياً)</li>
                            <li>المساعدة البحثية في الجامعة</li>
                        </ul>
                    </div>
                </div>
                <div class="content-card fade-in">
                    <div class="card-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <h3 class="card-title">السكن والمعيشة</h3>
                    <div class="card-content">
                        <p>نصائح للسكن في ألمانيا:</p>
                        <ul style="margin-top: 1rem; padding-right: 1rem;">
                            <li>ابدأ البحث عن السكن مبكراً</li>
                            <li>فكر في السكن الطلابي (Studentenwohnheim)</li>
                            <li>تعلم عن نظام التأمين الصحي</li>
                            <li>افتح حساباً بنكياً ألمانياً</li>
                        </ul>
                    </div>
                </div>
                <div class="content-card fade-in">
                    <div class="card-icon">
                        <i class="fas fa-briefcase"></i>
                    </div>
                    <h3 class="card-title">البحث عن عمل</h3>
                    <div class="card-content">
                        <p>استراتيجيات فعالة للحصول على وظيفة:</p>
                        <ul style="margin-top: 1rem; padding-right: 1rem;">
                            <li>اكتب سيرة ذاتية ألمانية احترافية</li>
                            <li>استخدم مواقع مثل Xing و StepStone</li>
                            <li>قدم على التدريب العملي (Praktikum)</li>
                            <li>احضر معارض التوظيف</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Final Inspirational Quote -->
    <section class="quote-section">
        <div class="container">
            <div class="quote">
                "إلى كل شاب عراقي يحلم بمستقبل أفضل: الطريق صعب لكنه ممكن. ثق بنفسك، اعمل بجد، ولا تنسَ جذورك. أنت قادر على تحقيق المعجزات."
            </div>
            <div class="quote-author">- أحمد محمد الخالدي</div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="section">
        <div class="container">
            <h2 class="section-title">تواصل معي</h2>
            <div style="text-align: center; max-width: 600px; margin: 0 auto;">
                <p style="font-size: 1.2rem; margin-bottom: 2rem; color: #4a5568;">
                    أسعد دائماً بالتواصل مع الشباب العراقي والعربي الطموح. لا تتردد في التواصل معي للاستشارة أو المساعدة في رحلتك المهنية.
                </p>
                <div class="content-grid" style="margin-top: 3rem;">
                    <div class="content-card">
                        <div class="card-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <h3 class="card-title">البريد الإلكتروني</h3>
                        <div class="card-content">
                            <p><EMAIL></p>
                        </div>
                    </div>
                    <div class="content-card">
                        <div class="card-icon">
                            <i class="fab fa-linkedin"></i>
                        </div>
                        <h3 class="card-title">لينكد إن</h3>
                        <div class="card-content">
                            <p>linkedin.com/in/ahmed-alkhalidi</p>
                        </div>
                    </div>
                    <div class="content-card">
                        <div class="card-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <h3 class="card-title">واتساب</h3>
                        <div class="card-content">
                            <p>+49 176 1234 5678</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="social-links">
                <a href="#" title="فيسبوك"><i class="fab fa-facebook-f"></i></a>
                <a href="#" title="تويتر"><i class="fab fa-twitter"></i></a>
                <a href="#" title="لينكد إن"><i class="fab fa-linkedin-in"></i></a>
                <a href="#" title="إنستغرام"><i class="fab fa-instagram"></i></a>
                <a href="#" title="يوتيوب"><i class="fab fa-youtube"></i></a>
            </div>
            <p style="margin-bottom: 1rem; font-size: 1.1rem;">
                "كن التغيير الذي تريد أن تراه في العالم"
            </p>
            <p style="opacity: 0.8;">
                © 2024 أحمد محمد الخالدي - مهندس ميكانيكي عراقي في ألمانيا
            </p>
            <p style="opacity: 0.6; margin-top: 1rem; font-size: 0.9rem;">
                تم تصميم هذا الموقع لإلهام الشباب العربي والعراقي لتحقيق أحلامهم
            </p>
        </div>
    </footer>

    <!-- JavaScript for Animations and Interactions -->
    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Fade in animation on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        // Observe all fade-in elements
        document.querySelectorAll('.fade-in').forEach(el => {
            observer.observe(el);
        });

        // Add mobile menu toggle functionality
        const createMobileMenu = () => {
            const nav = document.querySelector('nav');
            const navLinks = document.querySelector('.nav-links');

            // Create mobile menu button
            const mobileMenuBtn = document.createElement('button');
            mobileMenuBtn.innerHTML = '<i class="fas fa-bars"></i>';
            mobileMenuBtn.style.cssText = `
                display: none;
                background: none;
                border: none;
                font-size: 1.5rem;
                color: #4a5568;
                cursor: pointer;
            `;

            // Add mobile menu functionality
            mobileMenuBtn.addEventListener('click', () => {
                navLinks.classList.toggle('mobile-active');
            });

            nav.appendChild(mobileMenuBtn);

            // Show mobile menu button on small screens
            const checkScreenSize = () => {
                if (window.innerWidth <= 768) {
                    mobileMenuBtn.style.display = 'block';
                    navLinks.style.cssText = `
                        position: fixed;
                        top: 70px;
                        right: -100%;
                        width: 100%;
                        height: calc(100vh - 70px);
                        background: white;
                        flex-direction: column;
                        justify-content: flex-start;
                        align-items: center;
                        padding-top: 2rem;
                        transition: right 0.3s ease;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    `;
                } else {
                    mobileMenuBtn.style.display = 'none';
                    navLinks.style.cssText = '';
                    navLinks.classList.remove('mobile-active');
                }
            };

            // Add CSS for mobile menu active state
            const style = document.createElement('style');
            style.textContent = `
                .nav-links.mobile-active {
                    right: 0 !important;
                }
                .nav-links.mobile-active a {
                    padding: 1rem 0;
                    border-bottom: 1px solid #eee;
                    width: 80%;
                    text-align: center;
                }
            `;
            document.head.appendChild(style);

            window.addEventListener('resize', checkScreenSize);
            checkScreenSize();
        };

        // Initialize mobile menu
        createMobileMenu();

        // Add scroll effect to header
        window.addEventListener('scroll', () => {
            const header = document.querySelector('header');
            if (window.scrollY > 100) {
                header.style.background = 'rgba(255, 255, 255, 0.98)';
                header.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.15)';
            } else {
                header.style.background = 'rgba(255, 255, 255, 0.95)';
                header.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
            }
        });

        // Add typing effect to hero title
        const heroTitle = document.querySelector('.hero h1');
        const originalText = heroTitle.textContent;
        heroTitle.textContent = '';

        let i = 0;
        const typeWriter = () => {
            if (i < originalText.length) {
                heroTitle.textContent += originalText.charAt(i);
                i++;
                setTimeout(typeWriter, 100);
            }
        };

        // Start typing effect after page load
        window.addEventListener('load', () => {
            setTimeout(typeWriter, 1000);
        });

        // Add click effect to cards
        document.querySelectorAll('.content-card').forEach(card => {
            card.addEventListener('click', function() {
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });

        // Add parallax effect to hero section
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const hero = document.querySelector('.hero');
            const rate = scrolled * -0.5;
            hero.style.transform = `translateY(${rate}px)`;
        });

        console.log('🚀 موقع المهندس العراقي تم تحميله بنجاح!');
        console.log('💡 "النجاح يبدأ بخطوة واحدة نحو الحلم"');
    </script>
</body>
</html>